import React from 'react';

const APIKeyManager: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">API Key Management</h1>
      <div className="bg-white shadow rounded-lg p-6">
        <p className="text-gray-600">API key management functionality will be implemented here.</p>
      </div>
    </div>
  );
};

export default APIKeyManager;
