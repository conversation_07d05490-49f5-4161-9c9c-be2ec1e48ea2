{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "axios": "^1.11.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^6.30.1", "react-scripts": "^4.0.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "NODE_OPTIONS=--openssl-legacy-provider HOST=0.0.0.0 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.6"}}