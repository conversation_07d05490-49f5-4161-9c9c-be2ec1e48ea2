from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
import os
from contextlib import asynccontextmanager

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./spec_kit_web.db")

# Create async engine
engine = create_async_engine(
    DATABASE_URL,
    echo=True,  # Set to False in production
    future=True
)

# Create session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False
)

# Base class for models
Base = declarative_base()

@asynccontextmanager
async def get_db_session():
    """Async context manager for database sessions."""
    session = AsyncSessionLocal()
    try:
        yield session
        await session.commit()
    except Exception:
        await session.rollback()
        raise
    finally:
        await session.close()

async def init_db():
    """Initialize database by creating all tables."""
    async with engine.begin() as conn:
        # Import all models to ensure they are registered with Base
        from .models.user import UserModel, APIKeyModel
        from .models.project import ProjectModel, ProjectFileModel
        
        await conn.run_sync(Base.metadata.create_all)

async def close_db():
    """Close database connections."""
    await engine.dispose()

# For use in FastAPI lifespan events
async def lifespan(app):
    """Lifespan handler for FastAPI app."""
    await init_db()
    yield
    await close_db()
