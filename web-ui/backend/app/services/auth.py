from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
import jwt
from passlib.context import <PERSON><PERSON><PERSON>ontext
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession

from ..models.user import get_user_by_email, get_user_by_id, get_api_key_by_key, UserModel
from ..database import get_db_session

# JWT configuration
SECRET_KEY = "your-secret-key-here"  # In production, use environment variable
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def authenticate_user(email: str, password: str) -> Optional[UserModel]:
    async with get_db_session() as session:
        user = await get_user_by_email(session, email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user

async def get_current_user(token: str) -> UserModel:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except jwt.JWTError:
        raise credentials_exception
    
    async with get_db_session() as session:
        user = await get_user_by_id(session, user_id)
        if user is None:
            raise credentials_exception
        return user

async def verify_api_key(api_key: str) -> bool:
    async with get_db_session() as session:
        api_key_obj = await get_api_key_by_key(session, api_key)
        return api_key_obj is not None

async def get_user_from_api_key(api_key: str) -> Optional[UserModel]:
    async with get_db_session() as session:
        api_key_obj = await get_api_key_by_key(session, api_key)
        if not api_key_obj:
            return None
        user = await get_user_by_id(session, api_key_obj.user_id)
        return user

async def register_user(email: str, password: str) -> UserModel:
    async with get_db_session() as session:
        # Check if user already exists
        existing_user = await get_user_by_email(session, email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create new user
        hashed_password = get_password_hash(password)
        user = await create_user(session, email, hashed_password)
        return user

async def create_user(session: AsyncSession, email: str, hashed_password: str):
    from ..models.user import create_user as create_user_db
    return await create_user_db(session, email, hashed_password)

async def login_user(email: str, password: str) -> dict:
    user = await authenticate_user(email, password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.id}, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_id": user.id,
        "email": user.email
    }
