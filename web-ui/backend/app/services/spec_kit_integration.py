import asyncio
import subprocess
import json
import os
import tempfile
import shutil
from typing import Dict, Optional
from pathlib import Path

from .websocket_manager import WebSocketManager
from ..database import get_db_session
from ..models.project import add_project_file, update_project_status, ProjectStatus, get_project
from .auth import get_user_from_api_key

class SpecKitIntegration:
    def __init__(self):
        self.active_tasks: Dict[str, dict] = {}
        self.websocket_manager = WebSocketManager()

    async def generate_specification(self, task_id: str, project_id: str, description: str, api_key: Optional[str] = None):
        """Generate specification using Spec Kit CLI"""
        try:
            # Update task status
            self.active_tasks[task_id] = {
                "status": "processing",
                "project_id": project_id,
                "progress": 0
            }
            
            await self.websocket_manager.broadcast_task_update(task_id, "processing", 0)
            
            # Get project directory
            async with get_db_session() as session:
                project = await get_project(session, project_id)
                if not project:
                    raise ValueError(f"Project {project_id} not found")
                
                # Create temporary directory for generation
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # Initialize Spec Kit project
                    await self._run_spec_kit_command(
                        task_id, 
                        ["specify", "init", "--here", "--ignore-agent-tools"],
                        cwd=temp_path
                    )
                    
                    # Create specification
                    spec_result = await self._run_spec_kit_command(
                        task_id,
                        ["specify", "new_feature", description],
                        cwd=temp_path
                    )
                    
                    # Read generated files
                    spec_files = list(temp_path.glob("specs/**/*.md"))
                    for spec_file in spec_files:
                        content = spec_file.read_text()
                        file_type = spec_file.stem
                        
                        # Save to database
                        await add_project_file(session, project_id, file_type, content)
                        
                        # Broadcast to WebSocket
                        await self.websocket_manager.broadcast_generation_output(
                            task_id, file_type, content
                        )
                    
                    # Update project status
                    await update_project_status(session, project_id, ProjectStatus.SPECIFICATION)
                    
                    # Mark task as completed
                    self.active_tasks[task_id]["status"] = "completed"
                    self.active_tasks[task_id]["progress"] = 100
                    
                    await self.websocket_manager.broadcast_completion(task_id, {
                        "project_id": project_id,
                        "files_generated": len(spec_files)
                    })
                    
        except Exception as e:
            # Handle errors
            self.active_tasks[task_id]["status"] = "failed"
            self.active_tasks[task_id]["error"] = str(e)
            
            await self.websocket_manager.broadcast_error(task_id, str(e))
            
            # Update project status if it exists
            try:
                async with get_db_session() as session:
                    await update_project_status(session, project_id, ProjectStatus.FAILED)
            except Exception:
                pass
            
            raise

    async def _run_spec_kit_command(self, task_id: str, command: list, cwd: Path) -> str:
        """Run Spec Kit CLI command and handle output streaming"""
        full_command = ["uvx", "--from", "git+https://github.com/github/spec-kit.git"] + command
        
        await self.websocket_manager.broadcast_task_update(
            task_id, "running_command", 
            data={"command": " ".join(full_command)}
        )
        
        process = await asyncio.create_subprocess_exec(
            *full_command,
            cwd=cwd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout_chunks = []
        stderr_chunks = []
        
        # Read stdout and stderr concurrently
        async def read_stream(stream, is_stdout=True):
            while True:
                chunk = await stream.read(1024)
                if not chunk:
                    break
                text = chunk.decode()
                
                if is_stdout:
                    stdout_chunks.append(text)
                    # Broadcast output to WebSocket
                    await self.websocket_manager.broadcast_generation_output(
                        task_id, "stdout", text, chunk=True
                    )
                else:
                    stderr_chunks.append(text)
                    # Broadcast errors to WebSocket
                    await self.websocket_manager.broadcast_generation_output(
                        task_id, "stderr", text, chunk=True
                    )
        
        # Read both streams concurrently
        await asyncio.gather(
            read_stream(process.stdout, True),
            read_stream(process.stderr, False)
        )
        
        # Wait for process to complete
        return_code = await process.wait()
        
        stdout = "".join(stdout_chunks)
        stderr = "".join(stderr_chunks)
        
        if return_code != 0:
            raise Exception(f"Command failed with return code {return_code}: {stderr}")
        
        return stdout

    def get_task_status(self, task_id: str) -> dict:
        """Get status of a generation task"""
        task = self.active_tasks.get(task_id, {})
        return {
            "task_id": task_id,
            "status": task.get("status", "unknown"),
            "progress": task.get("progress", 0),
            "error": task.get("error"),
            "project_id": task.get("project_id")
        }

    async def generate_plan(self, task_id: str, project_id: str, tech_stack: str, api_key: Optional[str] = None):
        """Generate implementation plan (to be implemented)"""
        # Similar to generate_specification but for /plan command
        pass

    async def generate_tasks(self, task_id: str, project_id: str, api_key: Optional[str] = None):
        """Generate implementation tasks (to be implemented)"""
        # Similar to generate_specification but for /tasks command
        pass

    def cleanup_task(self, task_id: str):
        """Clean up task resources"""
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
