from fastapi import WebSocket
from typing import Dict, List
import json
import asyncio

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.task_subscriptions: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, task_id: str):
        await websocket.accept()
        self.active_connections[task_id] = websocket
        
        # Add to task subscriptions
        if task_id not in self.task_subscriptions:
            self.task_subscriptions[task_id] = []
        self.task_subscriptions[task_id].append(websocket)

    def disconnect(self, task_id: str):
        if task_id in self.active_connections:
            del self.active_connections[task_id]
        
        # Remove from task subscriptions
        if task_id in self.task_subscriptions:
            self.task_subscriptions[task_id] = [
                ws for ws in self.task_subscriptions[task_id]
                if not ws.client_state.CLOSED
            ]
            if not self.task_subscriptions[task_id]:
                del self.task_subscriptions[task_id]

    async def send_message(self, task_id: str, message: dict):
        """Send message to all connections subscribed to a task"""
        if task_id in self.task_subscriptions:
            for websocket in self.task_subscriptions[task_id]:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    print(f"Error sending message to websocket: {e}")
                    # Remove disconnected websocket
                    self.task_subscriptions[task_id].remove(websocket)

    async def broadcast_task_update(self, task_id: str, status: str, progress: int = None, data: dict = None):
        """Broadcast task update to all subscribers"""
        message = {
            "type": "task_update",
            "task_id": task_id,
            "status": status,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        if progress is not None:
            message["progress"] = progress
        
        if data is not None:
            message["data"] = data
        
        await self.send_message(task_id, message)

    async def broadcast_generation_output(self, task_id: str, output_type: str, content: str, chunk: bool = False):
        """Broadcast generation output (spec, plan, tasks, etc.)"""
        message = {
            "type": "generation_output",
            "task_id": task_id,
            "output_type": output_type,
            "content": content,
            "chunk": chunk,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        await self.send_message(task_id, message)

    async def broadcast_error(self, task_id: str, error_message: str, error_type: str = "generation_error"):
        """Broadcast error message"""
        message = {
            "type": "error",
            "task_id": task_id,
            "error_type": error_type,
            "message": error_message,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        await self.send_message(task_id, message)

    async def broadcast_completion(self, task_id: str, result: dict = None):
        """Broadcast task completion"""
        message = {
            "type": "completion",
            "task_id": task_id,
            "status": "completed",
            "timestamp": asyncio.get_event_loop().time()
        }
        
        if result:
            message["result"] = result
        
        await self.send_message(task_id, message)

    def get_connection_count(self, task_id: str) -> int:
        """Get number of active connections for a task"""
        if task_id in self.task_subscriptions:
            return len(self.task_subscriptions[task_id])
        return 0

    async def close_all_connections(self):
        """Close all active WebSocket connections"""
        for websocket in self.active_connections.values():
            try:
                await websocket.close()
            except Exception:
                pass
        self.active_connections.clear()
        self.task_subscriptions.clear()
