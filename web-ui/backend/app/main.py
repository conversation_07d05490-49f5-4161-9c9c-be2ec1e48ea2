from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import asyncio
import json
import subprocess
import os
import uuid
from datetime import datetime

from .models.user import User, <PERSON><PERSON><PERSON>, UserCreate, UserLogin
from .models.project import Project, ProjectStatus
from .services.auth import get_current_user, verify_api_key, login_user, register_user
from .services.spec_kit_integration import SpecKitIntegration
from .services.websocket_manager import WebSocketManager

app = FastAPI(
    title="Spec Kit Web UI API",
    description="Web interface for Spec-Driven Development with Spec Kit",
    version="0.1.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000", 
        "http://127.0.0.1:3000",
        "http://localhost:3002",
        "http://127.0.0.1:3002", 
        "http://0.0.0.0:3002",
        "http://*************:3002",
        "http://**************:3002"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()
websocket_manager = WebSocketManager()
spec_kit = SpecKitIntegration()

# Models
class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None

class ProjectResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    status: str
    created_at: datetime
    updated_at: datetime

class APIKeyCreate(BaseModel):
    name: str
    description: Optional[str] = None

class APIKeyResponse(BaseModel):
    id: str
    key: str
    name: str
    description: Optional[str] = None
    created_at: datetime

class SpecificationRequest(BaseModel):
    project_id: str
    description: str
    api_key: Optional[str] = None

class GenerationResponse(BaseModel):
    task_id: str
    status: str
    message: str

# Routes
@app.get("/")
async def root():
    return {"message": "Spec Kit Web UI API"}

@app.post("/api/auth/register", status_code=status.HTTP_201_CREATED)
async def register(user_data: UserCreate):
    try:
        user = await register_user(user_data.email, user_data.password)
        return {"message": "User created successfully", "user_id": user.id}
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )

@app.post("/api/auth/login")
async def login(login_data: UserLogin):
    try:
        result = await login_user(login_data.email, login_data.password)
        return result
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@app.get("/api/auth/me")
async def get_current_user_info(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    user = await get_current_user(credentials.credentials)
    return {
        "id": user.id,
        "email": user.email,
        "created_at": user.created_at
    }

@app.post("/api/projects", response_model=ProjectResponse)
async def create_project(
    project: ProjectCreate,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    user = await get_current_user(credentials.credentials)
    project_obj = await Project.create(
        name=project.name,
        description=project.description,
        user_id=user.id
    )
    return project_obj

@app.get("/api/projects", response_model=List[ProjectResponse])
async def list_projects(
    search: Optional[str] = None,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    user = await get_current_user(credentials.credentials)
    projects = await Project.list(user.id, search)
    return projects

@app.post("/api/specify", response_model=GenerationResponse)
async def create_specification(
    request: SpecificationRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    user = await get_current_user(credentials.credentials)
    
    # Verify project belongs to user
    project = await Project.get(request.project_id)
    if not project or project.user_id != user.id:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Create generation task
    task_id = str(uuid.uuid4())
    
    # Start async generation
    asyncio.create_task(
        spec_kit.generate_specification(
            task_id=task_id,
            project_id=request.project_id,
            description=request.description,
            api_key=request.api_key
        )
    )
    
    return GenerationResponse(
        task_id=task_id,
        status="processing",
        message="Specification generation started"
    )

@app.post("/api/api-keys", response_model=APIKeyResponse)
async def create_api_key(
    api_key_data: APIKeyCreate,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    user = await get_current_user(credentials.credentials)
    api_key = await APIKey.create(
        user_id=user.id,
        name=api_key_data.name,
        description=api_key_data.description
    )
    return api_key

@app.get("/api/api-keys", response_model=List[APIKeyResponse])
async def list_api_keys(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    user = await get_current_user(credentials.credentials)
    api_keys = await APIKey.list(user.id)
    return api_keys

@app.websocket("/ws/generation/{task_id}")
async def websocket_generation(websocket: WebSocket, task_id: str):
    await websocket_manager.connect(websocket, task_id)
    try:
        while True:
            data = await websocket.receive_text()
            # Handle incoming messages if needed
            await websocket.send_text(json.dumps({"type": "ack", "data": data}))
    except WebSocketDisconnect:
        websocket_manager.disconnect(task_id)

@app.get("/api/generation/{task_id}/status")
async def get_generation_status(task_id: str):
    status = spec_kit.get_task_status(task_id)
    return status

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
