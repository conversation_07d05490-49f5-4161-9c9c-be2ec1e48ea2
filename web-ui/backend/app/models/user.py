from typing import Optional, List
from datetime import datetime
import uuid
from pydantic import BaseModel
import sqlalchemy as sa
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.future import select

Base = declarative_base()

class UserModel(Base):
    __tablename__ = "users"
    
    id = sa.Column(sa.String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = sa.Column(sa.String, unique=True, nullable=False)
    hashed_password = sa.Column(sa.String, nullable=False)
    created_at = sa.Column(sa.DateTime, default=datetime.utcnow)
    updated_at = sa.Column(sa.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class APIKeyModel(Base):
    __tablename__ = "api_keys"
    
    id = sa.Column(sa.String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = sa.Column(sa.String, sa.ForeignKey("users.id"), nullable=False)
    key = sa.Column(sa.String, unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    name = sa.Column(sa.String, nullable=False)
    description = sa.Column(sa.String, nullable=True)
    created_at = sa.Column(sa.DateTime, default=datetime.utcnow)
    is_active = sa.Column(sa.Boolean, default=True)

# Pydantic models
class User(BaseModel):
    id: str
    email: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class APIKey(BaseModel):
    id: str
    key: str
    name: str
    description: Optional[str] = None
    created_at: datetime
    is_active: bool = True
    
    class Config:
        from_attributes = True

class UserCreate(BaseModel):
    email: str
    password: str

class UserLogin(BaseModel):
    email: str
    password: str

# Database operations
async def get_user_by_email(session: AsyncSession, email: str) -> Optional[UserModel]:
    result = await session.execute(select(UserModel).where(UserModel.email == email))
    return result.scalar_one_or_none()

async def get_user_by_id(session: AsyncSession, user_id: str) -> Optional[UserModel]:
    result = await session.execute(select(UserModel).where(UserModel.id == user_id))
    return result.scalar_one_or_none()

async def create_user(session: AsyncSession, email: str, hashed_password: str) -> UserModel:
    user = UserModel(email=email, hashed_password=hashed_password)
    session.add(user)
    await session.commit()
    await session.refresh(user)
    return user

async def create_api_key(session: AsyncSession, user_id: str, name: str, description: Optional[str] = None) -> APIKeyModel:
    api_key = APIKeyModel(user_id=user_id, name=name, description=description)
    session.add(api_key)
    await session.commit()
    await session.refresh(api_key)
    return api_key

async def get_api_keys_by_user(session: AsyncSession, user_id: str) -> List[APIKeyModel]:
    result = await session.execute(
        select(APIKeyModel)
        .where(APIKeyModel.user_id == user_id)
        .where(APIKeyModel.is_active == True)
        .order_by(APIKeyModel.created_at.desc())
    )
    return result.scalars().all()

async def get_api_key_by_key(session: AsyncSession, api_key: str) -> Optional[APIKeyModel]:
    result = await session.execute(
        select(APIKeyModel)
        .where(APIKeyModel.key == api_key)
        .where(APIKeyModel.is_active == True)
    )
    return result.scalar_one_or_none()

async def revoke_api_key(session: AsyncSession, api_key_id: str) -> None:
    result = await session.execute(
        select(APIKeyModel).where(APIKeyModel.id == api_key_id)
    )
    api_key = result.scalar_one_or_none()
    if api_key:
        api_key.is_active = False
        await session.commit()
