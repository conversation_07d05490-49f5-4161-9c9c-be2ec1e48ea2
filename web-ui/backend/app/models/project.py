from typing import Optional, List
from datetime import datetime
import uuid
from enum import Enum
from pydantic import BaseModel
import sqlalchemy as sa
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import declarative_base
from sqlalchemy.future import select

from .user import Base

class ProjectStatus(str, Enum):
    DRAFT = "draft"
    SPECIFICATION = "specification"
    PLANNING = "planning"
    IMPLEMENTATION = "implementation"
    COMPLETED = "completed"
    FAILED = "failed"

class ProjectModel(Base):
    __tablename__ = "projects"
    
    id = sa.Column(sa.String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = sa.Column(sa.String, sa.ForeignKey("users.id"), nullable=False)
    name = sa.Column(sa.String, nullable=False)
    description = sa.Column(sa.String, nullable=True)
    status = sa.Column(sa.String, default=ProjectStatus.DRAFT.value)
    created_at = sa.Column(sa.DateTime, default=datetime.utcnow)
    updated_at = sa.Column(sa.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ProjectFileModel(Base):
    __tablename__ = "project_files"
    
    id = sa.Column(sa.String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = sa.Column(sa.String, sa.ForeignKey("projects.id"), nullable=False)
    file_type = sa.Column(sa.String, nullable=False)  # spec, plan, tasks, etc.
    content = sa.Column(sa.Text, nullable=False)
    version = sa.Column(sa.Integer, default=1)
    created_at = sa.Column(sa.DateTime, default=datetime.utcnow)

# Pydantic models
class Project(BaseModel):
    id: str
    user_id: str
    name: str
    description: Optional[str] = None
    status: ProjectStatus
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ProjectFile(BaseModel):
    id: str
    project_id: str
    file_type: str
    content: str
    version: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None

# Database operations
async def create_project(session: AsyncSession, user_id: str, name: str, description: Optional[str] = None) -> ProjectModel:
    project = ProjectModel(user_id=user_id, name=name, description=description)
    session.add(project)
    await session.commit()
    await session.refresh(project)
    return project

async def get_project(session: AsyncSession, project_id: str) -> Optional[ProjectModel]:
    result = await session.execute(select(ProjectModel).where(ProjectModel.id == project_id))
    return result.scalar_one_or_none()

async def list_projects(session: AsyncSession, user_id: str, search: Optional[str] = None) -> List[ProjectModel]:
    query = select(ProjectModel).where(ProjectModel.user_id == user_id)
    
    if search:
        query = query.where(ProjectModel.name.ilike(f"%{search}%"))
    
    query = query.order_by(ProjectModel.updated_at.desc())
    
    result = await session.execute(query)
    return result.scalars().all()

async def update_project_status(session: AsyncSession, project_id: str, status: ProjectStatus) -> Optional[ProjectModel]:
    result = await session.execute(select(ProjectModel).where(ProjectModel.id == project_id))
    project = result.scalar_one_or_none()
    
    if project:
        project.status = status.value
        await session.commit()
        await session.refresh(project)
    
    return project

async def add_project_file(session: AsyncSession, project_id: str, file_type: str, content: str) -> ProjectFileModel:
    # Get latest version
    result = await session.execute(
        select(ProjectFileModel)
        .where(ProjectFileModel.project_id == project_id)
        .where(ProjectFileModel.file_type == file_type)
        .order_by(ProjectFileModel.version.desc())
    )
    latest_file = result.scalar_one_or_none()
    
    version = 1
    if latest_file:
        version = latest_file.version + 1
    
    file = ProjectFileModel(
        project_id=project_id,
        file_type=file_type,
        content=content,
        version=version
    )
    session.add(file)
    await session.commit()
    await session.refresh(file)
    return file

async def get_project_files(session: AsyncSession, project_id: str, file_type: Optional[str] = None) -> List[ProjectFileModel]:
    query = select(ProjectFileModel).where(ProjectFileModel.project_id == project_id)
    
    if file_type:
        query = query.where(ProjectFileModel.file_type == file_type)
    
    query = query.order_by(ProjectFileModel.file_type, ProjectFileModel.version.desc())
    
    result = await session.execute(query)
    return result.scalars().all()

async def get_latest_project_file(session: AsyncSession, project_id: str, file_type: str) -> Optional[ProjectFileModel]:
    result = await session.execute(
        select(ProjectFileModel)
        .where(ProjectFileModel.project_id == project_id)
        .where(ProjectFileModel.file_type == file_type)
        .order_by(ProjectFileModel.version.desc())
    )
    return result.scalar_one_or_none()
